# Arena Doviz - System Architecture

## Overview
Arena Doviz is a web-based accounting and exchange office management system built on the ERPSAAS foundation, customized for currency exchange operations. The system provides comprehensive financial management with multi-currency support, location-based operations, and automated WhatsApp notifications.

## Technology Stack

### Backend
- **Framework**: Laravel 11.x
- **Admin Panel**: Filament 3.x
- **Database**: MySQL 8.0+
- **Queue System**: Redis/Database
- **File Storage**: Local/S3
- **PDF Generation**: <PERSON><PERSON> Snappy + wkhtmltopdf

### Frontend
- **UI Framework**: Filament (Livewire + Alpine.js)
- **Styling**: Tailwind CSS
- **Icons**: Heroicons
- **Charts**: Chart.js/ApexCharts

### Infrastructure
- **Web Server**: Nginx/Apache
- **PHP**: 8.2+
- **Cache**: Redis
- **Session**: Redis/Database
- **Queue Worker**: Supervisor

## System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
├─────────────────────────────────────────────────────────────┤
│  Web Browser (Filament Admin Panel)                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Dashboard   │ │ Transactions│ │ Reports     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   APPLICATION LAYER                         │
├─────────────────────────────────────────────────────────────┤
│  Laravel Application (MVC)                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Controllers │ │ Services    │ │ Jobs/Queues │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Models      │ │ Policies    │ │ Events      │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     DATA LAYER                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ MySQL       │ │ Redis       │ │ File        │          │
│  │ Database    │ │ Cache       │ │ Storage     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  EXTERNAL SERVICES                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ WhatsApp    │ │ Currency    │ │ SMS/Email   │          │
│  │ API         │ │ Exchange    │ │ Services    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. User Management System
- **Admin**: Full system access, user management, system configuration
- **Accountant/Branch Employee**: Transaction processing, customer management
- **Viewer**: Read-only access to reports and data
- **Courier**: Delivery management and receipt handling

### 2. Customer Management
- Customer profiles with complete information
- Automatic WhatsApp group creation (7-8 staff + customer)
- Transaction notifications via WhatsApp
- Balance tracking per customer per currency

### 3. Currency & Location Management
- Dynamic currency addition (USD, AED, IRR initially)
- Location-based currency rates (Istanbul, Tabriz, Tehran, Dubai, China)
- Real-time exchange rate updates
- Currency balance tracking per location

### 4. Transaction Engine
- **Buy/Sell Transactions**: Currency purchase/sale with customers
- **Internal Transfers**: Between customer accounts
- **SWIFT Transfers**: International wire transfers
- **Cash Deposits/Withdrawals**: Local currency handling
- **Commission Calculation**: Percentage or fixed amount

### 5. Delivery System
- In-person pickup with signed receipts
- Courier delivery with photo documentation
- Courier profile management
- Delivery tracking and confirmation

### 6. Reporting System
- Customer statements with date ranges
- Multi-currency balance reports
- Transaction history with filters
- Profit/loss analysis
- Export to PDF/Excel

## Database Schema Overview

### Core Tables
- `users` - System users with roles
- `companies` - Multi-company support
- `customers` - Customer information
- `currencies` - Currency definitions
- `locations` - Office locations
- `exchange_rates` - Currency rates by location/date
- `transactions` - All financial transactions
- `balances` - Customer balances by currency
- `deliveries` - Delivery records
- `couriers` - Courier information
- `documents` - File attachments

## Security Architecture

### Authentication & Authorization
- Laravel Sanctum for API authentication
- Role-based permissions via Spatie Permission
- Multi-factor authentication (optional)
- Session management with Redis

### Data Protection
- Encrypted sensitive data
- Audit logging for all transactions
- Soft deletes (no permanent deletion)
- Regular automated backups

### Access Control
- IP-based restrictions (optional)
- Rate limiting on API endpoints
- CSRF protection
- XSS prevention

## Integration Points

### WhatsApp Integration
- Desktop WhatsApp automation
- Message templates for transactions
- Group management
- Notification queuing

### Currency Exchange APIs
- ExchangeRate-API integration
- Real-time rate updates
- Fallback rate sources
- Rate history tracking

### File Management
- Document upload/storage
- Receipt photo handling
- PDF generation for reports
- Backup and archival

## Performance Considerations

### Caching Strategy
- Redis for session storage
- Query result caching
- Exchange rate caching
- User permission caching

### Database Optimization
- Proper indexing on transaction tables
- Partitioning for large datasets
- Query optimization
- Connection pooling

### Scalability
- Horizontal scaling capability
- Load balancer support
- CDN for static assets
- Queue processing optimization

## Deployment Architecture

### Production Environment
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │────│   Web Servers   │────│   Database      │
│   (Nginx)       │    │   (PHP-FPM)     │    │   (MySQL)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   Queue Workers │
                       │   (Supervisor)  │
                       └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   Redis Cache   │
                       │   & Sessions    │
                       └─────────────────┘
```

### Development Environment
- Docker containers for consistency
- Local MySQL/Redis instances
- Hot reloading for development
- Testing database separation

## Monitoring & Logging

### Application Monitoring
- Laravel Telescope for debugging
- Error tracking with Sentry
- Performance monitoring
- Queue job monitoring

### System Monitoring
- Server resource monitoring
- Database performance tracking
- Cache hit rate monitoring
- API response time tracking

### Audit Logging
- All transaction changes
- User activity logging
- System configuration changes
- Data access logging

## Backup & Recovery

### Data Backup
- Daily automated database backups
- File storage backups
- Configuration backups
- Point-in-time recovery capability

### Disaster Recovery
- Database replication
- Failover procedures
- Data recovery testing
- Business continuity planning
