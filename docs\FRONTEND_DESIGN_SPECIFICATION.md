# Arena Doviz - Frontend Design Specification

## Design Overview

### Brand Identity
- **Brand Name**: Arena Doviz
- **Primary Colors**: 
  - Deep Blue: #1e3a8a (Primary brand color)
  - Gold/Amber: #f59e0b (Accent color for highlights)
  - Dark Gray: #374151 (Text and UI elements)
  - Light Gray: #f3f4f6 (Background and subtle elements)
- **Typography**: 
  - Primary: Inter (Clean, modern sans-serif)
  - Secondary: Roboto (For data tables and numbers)
- **Logo**: Modern, professional design incorporating currency symbols
- **Style**: Clean, industrial-grade interface with emphasis on functionality

### Design Principles
1. **Clarity**: Clear information hierarchy and intuitive navigation
2. **Efficiency**: Streamlined workflows for frequent operations
3. **Reliability**: Consistent UI patterns and predictable interactions
4. **Accessibility**: WCAG 2.1 AA compliance for all users
5. **Responsiveness**: Optimized for desktop, tablet, and mobile devices

## Layout Structure

### Main Application Layout
```
┌─────────────────────────────────────────────────────────────┐
│                    Header Navigation                         │
├─────────────────────────────────────────────────────────────┤
│ Sidebar │                Main Content Area                   │
│ Menu    │                                                   │
│         │  ┌─────────────────────────────────────────────┐  │
│         │  │            Page Header                      │  │
│         │  ├─────────────────────────────────────────────┤  │
│         │  │                                             │  │
│         │  │            Content Body                     │  │
│         │  │                                             │  │
│         │  │                                             │  │
│         │  └─────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    Footer Information                        │
└─────────────────────────────────────────────────────────────┘
```

### Header Navigation
- **Logo**: Arena Doviz logo (left side)
- **User Menu**: User avatar, name, and dropdown (right side)
- **Notifications**: Bell icon with notification count
- **Language Selector**: Current language with dropdown
- **Quick Actions**: Frequently used action buttons

### Sidebar Navigation
- **Dashboard**: Overview and key metrics
- **Transactions**: All transaction types and management
- **Customers**: Customer management and profiles
- **Reports**: Financial reports and statements
- **Settings**: System configuration and preferences
- **Administration**: User management and system settings

## Page Designs

### 1. Dashboard Page

#### Layout Components
- **Key Metrics Cards**: 4-column grid showing:
  - Total Company Balance (all currencies)
  - Today's Transaction Count
  - Pending Approvals
  - Active Customers
- **Quick Actions Panel**: Common transaction shortcuts
- **Recent Transactions Table**: Last 10 transactions with status
- **Currency Rates Widget**: Current buy/sell rates by location
- **Balance Alerts**: Low balance warnings and notifications

#### Visual Design
```
┌─────────────────────────────────────────────────────────────┐
│  Dashboard                                    🔔 Admin ▼    │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐            │
│ │ $125K   │ │   47    │ │    3    │ │  156    │            │
│ │ Balance │ │ Today   │ │Pending  │ │Customers│            │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘            │
│                                                             │
│ Quick Actions:                                              │
│ [New Buy] [New Sell] [Transfer] [Cash Deposit]             │
│                                                             │
│ Recent Transactions                          [View All]     │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ #001 | Buy  | John Doe    | $1,000 | Completed | 10:30 │ │
│ │ #002 | Sell | Jane Smith  | €500   | Pending   | 11:15 │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. Transaction Management Page

#### Transaction List View
- **Filter Panel**: Date range, customer, currency, status, location
- **Search Bar**: Global search across all transaction fields
- **Action Buttons**: New Transaction, Export, Bulk Actions
- **Data Table**: Sortable columns with pagination
  - Transaction Number
  - Date/Time
  - Customer Name
  - Type (Buy/Sell/Transfer)
  - Amount and Currency
  - Rate
  - Commission
  - Status
  - Actions (View, Edit, Approve, Cancel)

#### Transaction Form Design
```
┌─────────────────────────────────────────────────────────────┐
│  New Transaction                              [Save] [Cancel]│
├─────────────────────────────────────────────────────────────┤
│ Transaction Type: ● Buy ○ Sell ○ Transfer ○ SWIFT           │
│                                                             │
│ Customer: [Search Customer.....................] [+ New]    │
│ Currency: [USD ▼]  Location: [Istanbul ▼]                  │
│                                                             │
│ Amount: [____________] Rate: [____________]                  │
│ Commission: [____] [%▼] Currency: [USD▼]                   │
│                                                             │
│ Bank Details (Optional):                                    │
│ Transaction #: [________________]                           │
│ Bank Name: [____________________]                           │
│                                                             │
│ Notes: [_________________________________________]          │
│        [_________________________________________]          │
│                                                             │
│ Documents: [Choose Files] [📎 receipt.pdf]                 │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Transaction Preview                                     │ │
│ │ Amount: $1,000.00                                       │ │
│ │ Rate: 1.0850                                           │ │
│ │ Commission: $15.00 (1.5%)                              │ │
│ │ Total: $1,015.00                                       │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3. Customer Management Page

#### Customer List View
- **Search and Filter**: Name, phone, company, status
- **Customer Cards**: Grid or list view with key information
- **Quick Actions**: Add Customer, Import, Export
- **Bulk Operations**: Status changes, group actions

#### Customer Profile Design
```
┌─────────────────────────────────────────────────────────────┐
│  Customer Profile: John Doe                   [Edit] [Delete]│
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐  Personal Information                       │
│ │   [Photo]   │  Name: John Doe                            │
│ │             │  Phone: +90 ************                   │
│ │             │  Company: ABC Trading Ltd.                 │
│ └─────────────┘  WhatsApp Group: #group_001                │
│                                                             │
│ Current Balances:                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ USD: $2,500.00 | EUR: €1,200.00 | TRY: ₺15,000.00     │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Recent Transactions                          [View All]     │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Date     | Type | Amount    | Status    | Actions       │ │
│ │ 15/01/25 | Buy  | $1,000    | Completed | [View]        │ │
│ │ 14/01/25 | Sell | €500      | Completed | [View]        │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Notes:                                                      │
│ [Regular customer, prefers USD transactions...]             │
└─────────────────────────────────────────────────────────────┘
```

### 4. Reports Page

#### Report Selection Interface
- **Report Categories**: Financial, Customer, Transaction, Regulatory
- **Quick Reports**: Pre-configured common reports
- **Custom Report Builder**: Drag-and-drop interface
- **Scheduled Reports**: Automated report generation

#### Statement Generation
```
┌─────────────────────────────────────────────────────────────┐
│  Generate Statement                           [Generate PDF] │
├─────────────────────────────────────────────────────────────┤
│ Customer: [Select Customer.....................] [All]      │
│ Date Range: [01/01/2025] to [31/01/2025]                   │
│ Currency: [All ▼] Location: [All ▼]                        │
│                                                             │
│ Include:                                                    │
│ ☑ Transaction Details  ☑ Balance Summary                   │
│ ☑ Commission Breakdown ☑ Exchange Rates                    │
│                                                             │
│ Format: ● PDF ○ Excel ○ CSV                                │
│                                                             │
│ Preview:                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ARENA DOVIZ - STATEMENT OF ACCOUNT                     │ │
│ │ Customer: John Doe                                      │ │
│ │ Period: 01/01/2025 - 31/01/2025                       │ │
│ │                                                         │ │
│ │ Date     | Description | Debit    | Credit   | Balance │ │
│ │ 15/01/25 | USD Purchase| $1,000.00|          | $1,000  │ │
│ │ 16/01/25 | Commission  | $15.00   |          | $985    │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Component Library

### Form Components

#### Input Fields
- **Text Input**: Standard text input with validation
- **Number Input**: Formatted number input with currency support
- **Select Dropdown**: Searchable dropdown with multi-select option
- **Date Picker**: Calendar-based date selection
- **File Upload**: Drag-and-drop file upload with preview

#### Buttons
- **Primary Button**: Main action buttons (blue background)
- **Secondary Button**: Secondary actions (gray outline)
- **Danger Button**: Destructive actions (red background)
- **Icon Button**: Icon-only buttons for compact spaces

#### Data Display
- **Data Table**: Sortable, filterable table with pagination
- **Card Component**: Information cards with consistent styling
- **Badge/Tag**: Status indicators and labels
- **Progress Bar**: Loading and progress indicators

### Navigation Components

#### Breadcrumbs
```
Home > Transactions > New Transaction
```

#### Tabs
```
[General] [Bank Details] [Documents] [Preview]
```

#### Pagination
```
← Previous  1  2  [3]  4  5  Next →
Showing 21-30 of 150 results
```

## Responsive Design

### Breakpoints
- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px
- **Desktop**: 1024px - 1439px
- **Large Desktop**: 1440px+

### Mobile Adaptations
- **Collapsible Sidebar**: Hamburger menu for navigation
- **Stacked Forms**: Single-column form layouts
- **Touch-Friendly**: Larger buttons and touch targets
- **Simplified Tables**: Horizontal scrolling or card view

### Tablet Adaptations
- **Flexible Grid**: 2-column layouts where appropriate
- **Condensed Navigation**: Compact sidebar with icons
- **Optimized Forms**: 2-column form layouts

## Accessibility Features

### WCAG 2.1 AA Compliance
- **Color Contrast**: Minimum 4.5:1 ratio for normal text
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and roles
- **Focus Indicators**: Clear focus states for all interactive elements

### Internationalization
- **RTL Support**: Right-to-left language support
- **Font Scaling**: Support for user font size preferences
- **Currency Formatting**: Locale-specific number and currency formatting
- **Date Formatting**: Locale-specific date and time formats

## Performance Considerations

### Loading States
- **Skeleton Screens**: Content placeholders during loading
- **Progressive Loading**: Load critical content first
- **Lazy Loading**: Load images and non-critical content on demand
- **Caching**: Client-side caching for frequently accessed data

### Optimization
- **Code Splitting**: Load only necessary JavaScript
- **Image Optimization**: Responsive images with appropriate formats
- **CSS Optimization**: Minimize and compress stylesheets
- **Bundle Size**: Keep JavaScript bundles under 250KB

## Error Handling

### Error States
- **Form Validation**: Inline validation with clear error messages
- **Network Errors**: Retry mechanisms and offline indicators
- **Permission Errors**: Clear messaging for access restrictions
- **System Errors**: User-friendly error pages with support contact

### Success States
- **Confirmation Messages**: Clear success feedback
- **Progress Indicators**: Show completion status
- **Next Steps**: Guide users to logical next actions
