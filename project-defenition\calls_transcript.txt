کارنسی یوس دی استانبول یوس دی تبریز یوس دی چین یعنی اون کارنسیهست نه لوکیشن دفتر می‌دونید چی میگم یعنی اون لوکیشنی که برای عرض در نظر گرفتیم اون مارک بغل پولهست
که توی استانبول این ارض ۴۰۰ تومنه توی تبریز ۴۵۰ه چون یوس دی انتخاب بکنی همشون میشن یه چیزی بعد از یک طرف یوس دی چند مدل نیستش که درسته یوس دی یه دونهست
درسته بله
یوس دی فقط بر اساس لوکیشن نرخش فرق می‌کنه یوس دی بر اساس لوکیشن نرخش فرق می‌کنه این من این چند بار خواستم این معامله رو صبر کنم مثلاً یه یه مورد دیگه‌ای هم هست ببین مثلاً یه چیزی عددایی که می‌نویسی سه تا سه تا جدا بشن
س تا سه تا جدا بشن
آره مثلاً ۱۰۰ مثلاً ۱ گان صدگان هزارگان که این شکلی جدا میشنا اون مدلی اینو جداش بکنی که وقتی طرف داره می‌زنه مثلاً طرف میاد میگه آقا برای من به اندازه یک میلیارد تومن شما چیز بده چی میگم بهش به اندازه یک میلیارد تومن عرض بده من اونجا می‌زنم که آقا ریال یک می رد تومن اون اتو کلکیولیتت اینجا به درد میخوره
من یک میلیونو اونجا می‌زنم بعد پایین فیشو می‌زنم نرخش ریتشو
مثلاً می‌زنم ۹۰ هزار تومن و این خودش اونو با این تقسیم می‌کنه یوس دیشو آپدیت می‌کنه
اوکی
یا مثلاً یکی میاد میگه که آقا من به اندازه یک میلیارد تومن تومن نیاز دارم از حساب من انقدر دلار بفروش
و ببین اونجا که اینا تبدیل تو درد ببین بای سل باشه این قسمت ترنزکشنمون یه ذره نیاز داره که مثلاً عوض بشه ببین وقتی شما آیآ رو انتخاب می‌کنی تو یوس دی این درسته ولی بایسل که باشه بایسل مثلاً این اول بایشو انتخاب می‌کنی بعد سلشو انتخاب می‌کنی بعد آخر سر که میخوای گزارش بگیری آخر سر تو این فقط داره این به همدیگه اینا رو تبدیل می‌کنه
ما نمی‌دونیم که چند خریدیم چند فروختیم سود سیستم درنمیاد اگر بایسل نباشه بعد مثلاً شاید آخر ماه گزارش بگیریم بگیم آقا ما این ماه چقدر خریدیم می‌دونی منظورم چیه؟
مثلاً ما بگیم که آقا ما آخر این ماه مثلاً ۱۰۰ میلیون دلار ارض خریدیم چقدرشو فروختیم؟ مثلاً ۷۰ هزار تاشو فروختیم
ا پس این مثلاً ۳۰ هزار ۳۰۰ هزار تاش کجا مونده مثلاً روی این موضوع بعد بایسل با شاید حتماً باشه خب یعنی چون معامله رو وقتی داری ثبت می‌کنی باید حتماً خرید و فروشش ثبت بشه ببین این شکلی میشه دیگه عملاً ببین ما ما یه معامله ثبت می‌کنیم بای یا خریدیم یا فروختیم بعد اون توی لیست اون معامله مشخصه روی اینکه مثلاً اگه به ۱۰ هزار دلار به فلانی فروختیم ۲۰ ۰۰۰ دلار به بهمانی فروختیم ۳۰ ۰۰۰ دلار به اون یکی فروختیم ۴۰ ۰۰۰ دلار به این فروختیم بعد فردا تا ساعت ۱۲ شروع ن اینا به فیش تومن دادن.
خب
این که شروع می‌کنه به فیش تومن دادن اونی که ۱۰ ۰۰۰ دلاره مثلاً ۹۰۰ میلیون تومن ما بهش بدهکاریم باید پول بدیم دیگه بهش
درسته
شروع ما منم عرض اینو گرفتم فروختم به یکی دیگه اون شروع می‌کنه فیش‌های تومنو میده من ثبت می‌کنم توی سیستم بعد اینجا باید یه مدلی ثبت بشه که تو د تا طرفم باشه
متوجهم بله تو د تا طرفم ثبت بشه ولی ما یه بار اینو ثبتش کنیم بگیم از حسن گرفتیم دادیم به حسین بابت معامله فلان
اوکی
این اینا رو اگه حالا من توضیح دادم آخه میخوام همینو ب آقای اسلفش منم یه توضیح بدم خدمتتون راجع به اینا مثلاً این قسمتی که گفتین که مشتری که میاد حالا یا یت میشه یا کردیتش میشه که مثلاً توی سیستم داره و می‌تونه این کارو بکنه یعنی مثلاً اکسچنجو انجام بده این قسمت پیاده سازی شده مثلاً من هم این ترنزکشنو داشتم براتون توضیح می‌دادم من این کارنسی اکسچنجو مثلاً ایجاد کردم البته متوجه یه سری تغییراتی که تو گفتین تو صحبتاتون شدم و حتما انجامشون میدم ولی حالا میخوام یه سری چیزایی هم که بودن اما شاید درست ندیدینشونو بهتون نشونشون بدم مثلا توی این قسمت تراکنش که داره انج انجام میشه کارنسی اکسچنج ما یه بلنس اینترز داریم که دقیقاً همین کاریو که میکنه که میگین یعنی مثلاً اینور ما یه عرضیو از مشتری میگیریم حالا قراره بهش واریز کنیم یه مشتری دیگه این یه سری بالانس اینتری داره مثلاً مثل همون استیتمنتی که به من نشون دادین توی چند بخش واریز کرده بود این این قسمت اضافه میشه یعنی موجودی بالانس این تراکنش کمکم میشه و آلرتش میاد تو سیستم که این مثلاً این تراکنش انجام شده است و می‌تونین اپروش کنین به عنوان چیز که این اپرو شده و وقتی آره و وقتی سن واتسپو می‌زنین برای هر مشت برای این مشتری که تراکنش کامل شده میره در مورد اینکه حالا کاستومرمون مثلاً دبیته یا کردیته اینجا هم مشخصه نگاه کنید الان مثلاً این بالانس سامری اینه که ما مثلاً عرض اماراتمون مثلاً ۲۳۰۰ تا این مشتریمون بدهکار به سیستم اینقدر ریال داره مثلاً اینقدرم دلار داره برای این مشتری برای حالا باز میگم متوجه تغییرات شدم و این قسمتو بایسلش میکنم که دقیقا مشخص بشه برای این هر چیزی هم که فرمودین مثلا یوس دی استانبول یوس دی چینه این پیاده سازی شده خیلی راحته مثلاً فقط یه اینجا من بیام بزنم حالا نمی‌دونم صفحه منو دارین یا نه
من دارم نگاه می‌کنم
مثلاً اینجا بزنم همونجوری که گفتین یوس دی مثلاً استا
ما یه کارنسی تعریف می‌کنیم دیگه از
آره یه کارنسی تعریف میکنیم و اون اون ریتم حالا الان من ریتو از دیتابیس میگیرم اونم که قرار شد به صورت دستی باشه و به این صورته حالا بازم میگم متوجه تغییراتتون شد تغییراتی که گفت شدم نتدایشون کردم پس من حس میکنم این قسمت و این تغییراتو باید انجام بدم که بتونیم باز دوباره کرکسیون کنیم با هم یا نه اگه نکته دیگهای هست بهم بگین
نه همین موضوع بود ببین مثلا من یه عکسی برات فرستادم دیدی به صورت کمبو باکس اسم بانک‌های ایرانی بودش بعد میومد اونا رو باز می‌کرد انتخاب می‌کرد یه دونه شماره پیگیریش واریز کنندهش کی بود این شکل یعنی فیش‌های تومنمون این شکلی ثبت بشن توی سیستم بعد مثلاً یهو باز می‌کنیم سیستمو مثلاً ۲۰ تا فیش تومنو همزمان می‌زنیم اوکی
توی سیستم بعد یه دونه سابمیتشو که می‌کنیم همه میاد آپدیت میشه هر دو طرف یعنی من اون ماهیت اکسچنج رو شما دقیقاً اون ماهیت اکسچنج بین‌المللی که هست اونو درک کردی ولی متأسفانه سیستم داخل ایران این شکلی نیست
سیستمی که ما توی ایران استفاده می‌کنیم با ایکس ای نمیدونم بایننس با صرافیهایی که اونجا هستن با اونا نیستن ببین ما الان عملاً الان یه دونه اون میرزاب نویس مدلی که گفتم بهت
یعنی این تعدادش میتونه بشه ۱۰۰ تا ردیف میتونه ه۰۰ تا ردیف باشه
اینا رو ما بتونیم به تفکیک مشتریامون دربیاریم
الان اینجا مثلاً شما برای دپوزیت پول کش دپوزیتو گذاشتی
اوکی بعد کش دپوزیتی که هست حالا اونجا داکیومنتم گذاشتی آپلود کنی
مثلاً طرف آخ رسیدی که گرفته رو بذاریم روی اون سیستم باشه اشکالی نداره ولی دیگه تو کش دپوزیت گزینه‌های دلیوری و نمی‌دونم این یکی اینا نباشن اونجا
بذارین یادداشت کن
کشتی یه مشتری انتخاب میشه یه دونه عرضش انتخاب میشه بعد مبلغش رو مثلاً بدهکاربستان کار می‌کنه
اوکی فقط مشتری و ارزش آره دیگه مثلاً وقتی داری واسه کسی کش دپوزیت می‌کنی اونجا دیگه دلیوری و این یکیا نباید باشن
اوکی
وقتی داری پول رو وارد سیستم می‌کنی ببین من صحبتم اینه که شما ذهنت این شکلیه که اینو پر می‌کنه همزمان با اون دلیوریشم می‌زنه میگهین پولی که دریافت کردی بده به فلانی
درسته
شما ذهنیتت اینه من میگم که آقا اینا دونه‌ای باشن یعنی کشت پوزیش یه دونه سابمیت بخوره عد که این آورد آیدی کارت رو داد گفت به این تحویل بده از یه منوی دیگه‌ای برای دلیوری بگیم که از حس دبیت نوت دبیت نوت میگیم دیگه نمونه دبیت نوتم فرستادم برات دیدی چطوری چاپ کرده بود
بله
یه دبیت نوت اون شکلی ما یه منوی دبیت نوت داشته باشیم توی اون منوی دبیت نوت بیایم انتخاب بکنیم که آقا از مشتری فلان به کوریر فلان یا به مشتری فلان داریم پول پرداخت مییم کن
اه
این همشو تو سیستم گذاشتی فقط جاهاشون یه ذره جسته گریختن
آره
که اگر اینا مثلاً اون مدلی که من میگم باشه اپراتور هم راحت ازش
من یه چیزی ازتون بپرسم آقای عسفروش مثلاً شما میتونین روی یه کاغذ مثلاً بگین مثلاً فلان صفحه باشه بعد اینجا مثلاً فلان دکمه‌ها اینا یه عکسی این مدلی برای من بگیرین یا براتون میگم مقدور نیست همچین کاری اگر فور الان مثلاً این کش دپوزیتو
بهت گفتم کش دپوزیت این شکلیه وقتی که ما توی سیستم میخوایم به کسی کش دپوزیت یعنی چی؟ یعنی داریم پول واریز می‌کنیم به حساب کسی بعد اون یه منوی ساده داره
میگیم که ۱۰۰۰ دلار کش دپوزیت کن رو اکانت وهاب آره آره
میگه مفهومش اینه یعنی کشت دفوزیت گزینه دیگه‌ای نداره بعد اونور میری توی پرداختات که همون میشه دبیت نوت توی دبیت نوت میگی از اکانت وهاب ۱۰۰۰ دلار بده به امیر حسین
اه
یعنی اون جاش فرق می‌کنه اصلاً توی سیستم‌هایی که صرافی ایجاد میشن اون شخصی که توی صندوقه داره پول رو می‌گیره بابت تحویل اون پوله هیچ قدرتی نداره یعنی اون صندوقداری که اونجا توی استانبوله میگیم آدرس میدیم ببریم پولو بده بهش
بعد می‌تونه کش دپوزیت بزنه. اون فقط می‌زنه که آقا ۱۰۰ دلار از بابت آقای فلانی دریافت کرد.
اوکی خب پس یعنی اینور توی دبیت نوتبیت نوتمون چیه؟ پرداخت نقدیه که آقا پرداخت نقدیتو انجام میدی اینطوری می‌زنی کوریرو انتخاب می‌کنی اسما میفته یه چاپ می‌گیریم به از مشتری یه امضا می‌گیریم روی این گزینه‌ای که بوده
پس یه قسمت که سیستم رو پیچیده می‌کنه چی قسمت تومنیه که قسمت د تومنی هم بیای اگر داخل بایسل بکنی ببین ما بایسل دلار در مقابل یورو هم داریم ها
ما دلار در مقابل یورو هم داریم میگیم که هر یورو ی۷۱ دلاره
اه
بعد وقتی توی سیستم می‌زنی که آقا من دلار یورو رو دارم می‌بندم اینم می‌تونی ثبت کنی داخلش ی بایسلی که انجام میدی چی میدی چی می‌گیری ما میگیم که یک میلیارد تومن تومن دادیم انقدر دلار گرفتیم بعد خریدیم دیگه اینو بعد میگیم که آقا داریم می‌فروشیم
اون بایسلم می‌دونی دیگه چه اتفاقی میفته
که با خب
مثلاً فروخت به ما بدهکار بس انکار شدنشو مشخص می‌کنه دیگه این سیستم
اون باسل بدهکار بستانکاریه رو درست می‌کنه من همیشه این بدهکار بستان کارو قاطی می‌کنم دبیت کردیت میشه دیگه همون
اه
وقتی مثلاً امیرحسین فروخت امیرحسین کردیتش به اندازه اون تومنه آپدیت میشه
بله
وهاب که از وهاب که خرید وهاب دبیتش به اندازه اون تومنی که باید به امیر ن بده ب بدهکار شد
اوکی
اینا رو اگر این دو سه تا آیتمو دو رو بندازی اینا اوکی بشن الباقیشم می‌تونم بهت بگم چون می‌دونی تا سیستم نیومده بالا از اونجایی هم که مثلاً اگر مثلاً نزدیک بودی مثلاً می‌گفتم یه د ساعت بیای دفتر بشینیم با همدیگه مثلاً همه اینا رو بنویسیم ولی الان خوشبختانه حالا آیوت بالائه گرفتی موضوع چیه شرایط که مثلاً فهمیدی یه ذره این فرم‌ها رو جابهجا بکنی
شما تهرانین الان عسفروش
نه من حبیزم
آها گفتم آخه یه چند روز دارم میرم تهران گفتم شاید اینجا ببینم تهران بودم تهران بودم دیگه این تعطیلیا رو اومدم تبریز اه خب
آخه پنجشنبه هم دارم از ایران میرم
به سلامتی انشاالله
پنجشنبه این هفتهم برم معلوم نیست مثلاً مهر ماه برمی‌گردم آخر مهر برمی‌گردم کی برمی‌گردم اونم معلوم نیست ولی اگر الان الا این توضیحاتی که بهت دادم اگر کافی بود که حالا اینا رو پیادهش بکن که اون قسمت بایسلمون درست بشه ماهیت سیستم در مومد
ببینید تا حد خیلی زیادی برام واضح بود خب من یه سری تغییراتیو میدم این مثلاً همین این قسمتهایی که الان امروز گفتینو حالا سعی کردم نتبرداری کنم صداتونم با اجازهتون ضبط کردم که بتونم دوباره گوش بدم اگه جای متوجه نشدم اینا رو سعی می‌کنم پیاده سازی کنم به قول شما مرحله به مرحله بریم اگه جای دیگهش نیاز به تغییر داره انجام بدیم با همدیگه ولی ت
الان ببین ریپورتتو تو درست کردی گزارشتو داری می‌گیری کارنسیو می‌تونیم از اونور تعریف بکنیم بعد یوزرامونو می‌تونیم تعریف بکنیم پرژنامونو گذاشتی فقط میمونه اون قسمت اصلی ترنزکشنمون که اون قسمت ترنزکشنمون هم یه دونه خرید و فروش باشه یه دونه دریافت پرداخت باشه این شکلی عین همون سیستم‌هایی که مثلاً الیان سال مثلاً توی سیستم حسابداری هست با اسلمون رو هم این مدلی که گذاشتی اوکیه یا سرویس فی داریم یا ر** داریم شاید هم ریت داشته باشیم هم سرویس فی داشته باشیم سرویس فیمون رو شما د مدل بذار یا یه عدد ثابت یا ۱د بعد مثلاً این چه مدلی انتخاب میشه وقتی شما داری یوسبی می‌خری بعد مثلاً شده ۱۰۰ دلار ی مثلاً هز۰ دلار یوس
سرویس فی درصد می‌زنی دلار حساب کنه ورداره یا عدد خالی زدی مثلاً ۵۰ دلار ۵۰ زدی این ۵۰ دلار خودش ورداره
می‌دونی چی میگم؟
آره
اون قسمتی که سرویس فی می‌زنی اون عرض اولیه از روش از روی اون کارمزد نمی‌گیریم میگیم که آقا مثلاً شما ۱ میلیارد تومن دادی به اندازه مثلاً میلیارد دلار خریدی بعد سرویس فی تو چیه؟ سرویس فی تو ۱۰۰ دلار
ا
چون دیگه این تومنه تبدیل شده به دلار دیگه ما از روی دلاره کسر می‌کنیم دیگه این عددو
درسته؟
اوکی
اوکی
حتی اینجاشم می‌تونی یه یه کاری انجام بدی حالا اگر دیدی که این ای مدلی یه باکسی بغلش بذاری که این رو هم بتونیم درصدی انتخابش کنیم. شاید مثلاً یه لحظه یه جایی بود طرف گفت که نمی‌تونم دلار بدم ما اونو انتخاب کنیم چون معامله دواره دیگه تومن با دلار معامله شده اون قسمتی که عدد کاملو می‌زنی د تا ما آیتمی که انتخاب شده رو بندازه اونجا اه
متوجه شدی که
پس ب یه باکس بذاریم که بتونه انتخاب کنه حالا مثلاً میخواد این کارمزه از دلارش کم بشه یا مثلاً از ریالش ه
اوکی
آره آره آره ببین سرویس دی رو می‌زنیم ۱۰ اه
اوکی
۱و که زدیم جلوی سرویس دیه یه باکسی باشه که بتونه اینو ۱۰۰ یوس دی یا ۱۰۰ ریال باشه
اه
بعد بغل همون سرویس دیگه یه همین الان که گذاشتی درصد می‌تونی بزنی هم باشه ولی این رو محدودش کن که یا باید درصد بزنه یا عدد خاص بزنه هم درصد بزنه هم عدد خاص بزنه نباشه
اوکی
معمولاً ۷۰ در می‌زنیم‌ها بعد حتی خود اون درصد رو هم که زدیم اونجاهم انتخاب بکنیم که آقا این درصده از رو تومنش کم میشه یا از رو دلارش کم میشه
باشه چشمای عرفوش من این تغییراتو میدم نید الباقیو با همدیگه میریم جلو دیگه چونزعاتن نرم‌افزارهای حسابداری تحلیل می‌خوان همشون حالا ما دیگه هرچند سادهست نرم‌افزارمون مثلاً میگیم ساده سادهست این مدلیه و بازم بعضی چیزا رو تا خودت لمس نکنی
آره اینایی که تجربه کاری خودتونه خیلی تأثیر می‌ذاره روی اینکه این چه شکلی قراره بشه واقعاً مثلاً الان یه سری نکاتی گفتین خب خیلی تغییر میده اصلاً رابط کاربریو تا حدی که مناسبه دقیقاً همین شیوه کار شما بشه دیگه
اصلاً یه چیزی بهت بگم اگر این نرم‌افزارو کاملش بکنیم من همین الانش ۱۰۰ تا نسخهشو فروختم
به سلامتی باشه
این لحظه همکارایی که داریم خیلیشون اینو می‌خوان
اه
بعد نرم‌افزاری هم که هست موجوده خیلی گرونه
اه
نرم‌افزاری هم که نوشتن دیگه هر کسی حاضر نیست بره اونو بخری مثلاً ۸۰۰ ساله نمی لازمه نرم‌افزارو میگم
بعد هزار و نمیدونم تا سالانه می‌گیرن به ازای هر کو نمی‌دونم سیو رو می‌گیرن عددا این شکلی یه عجیب غریبی داره من دیگه اونو مثلاً ۵ نفر ۱۰ نفر از طراح‌های ایرانی استفاده می‌کنن. ا
باش جشن من پس کاراشو می‌کنمش ممنونم از توضیحاتتون. مرسی وقت گزارش اینه داریم بایکل رو قسمت بایکس رو اگه یه ذره درستش بکنیم من هر چقدر منوهامون تک کاربرتر تک خدماتی‌تر اونقدر بهتر.
اه
ببین من ذهنیتم رو وقتی که مثلاً بایسل رو نی اون دیگه فقط به خاطر اینه که یکی بدهکار شد یکی به استانکار شد یعنی اون هدفش اینه که معامله رو ثبت کنه دو سطر آپدیت بکنه روش
اه
دیدی دیگه اون فرستاده بودم دیدی چه شکلی کردیت دبیت می‌کرد دیگه سطر اول ازش موجودی دلارش کم می‌کرد بعد موجودی ریالش رو بدهکار می‌کرد بعد ثبت میشد که با چه ریتی این موجودی ریال بدهکار شده بعد هر فیش تومنی که می‌زدیم از اون مبلغ بدهکاریمون کم میشد
آره
بازم هر سؤالی داشتی هر قسمتش که مثلاً دیدی که مثلاً حالا چون با نیم ساعت یه ساعت که نمیشه مثلاً اینا رو تحلیل کرد هر قسمتی که داشتی می‌نوشتی سؤال بود برات اونو بپرس
باشه چشم ممنون از وقتی که گذاشتی مرسی دستت در
دست شما درد نکنه مرسی
مرسی
خدا بب جان چقدر باید شارژ کنم
۸۰۰
۸۰۰ هزار تومن
نه ۸۰ تو خوردی
تی نون نهار
نه کارش فریکنه کار دست
آره بب چ بهش گفت کارت اشتباه بود
یه چالش گفت اشتباه بودم

صدای منو دارین؟
آره ببین یه موضوعی پیش اومد
توی قسمتی که آپلود داکیومنت هست مثلاً رفتم الان تو اکسترنال ترنسفر ببین ماهیت اکسترنال ترنسفررو خوب تعریف کردی مثلاً میگی که کارنسی یوس دی مثلاً ۱۰ هزار دلار مثلاً بعد میای میگی که مثلاً فیش مثلاً رنسفر فیش مثلا فلان قد باشه
حالا مثلاً اینا باید درصداشون همه دستی وارد بشه دیگه چیز نباشه
بعد جونم به شما بگه که ب مثلا اینور بانک دیتیلشو گذاشتی ایبانشو میذاریم بعد مثلاً اسم هولدرشو گذاشتیم سویفت نیمشو گذاشتیم اینا خیلی خوبه ما توی اون قسمتهایی که داری به عنوان چیز میگن دیگه مثلاً دبیت نوت چیز می‌نویسیم یا حواله دیگهیتنس مثلاً طرف پول میده ما داریم بهش حواله می‌زنیم این قسمت حواله مثلاً کامله و اون پایین داکومنت گذاشتی
اه
اوکی یه دونه پایین داکیومنت گذاشتی اون داکیومنت رو یه طوری بکن که بشه چند تا آیتم رو آپلود کرد
چون میبین ممکنه مثلا لایسنس شرکتو بخوای آپلود کنی بعد مثلاً چیز پروانه کسب شرکتشونو داری اونجا که این مثلا توی سیستم بمونه بعد اونورم که یه دونه نوشتی نمیدونم پروپوز آف چیزشو ترنسفرشو مثلا دلیل پروپوز معمولان توی اون اینو یهش بکن که اگر چیزی هم ننوشتیم ننوشتیم یا مثلا توضیحی ننوشتیم ننوشتیم روش
اوکی
اوکی یعنی منظور ستاره دار نباشه که کامپلیت نشه
باشه چشم
اوکی
اوکی ت شما ت حالا بازم اگه داشتین می‌گشتین چیز دیگه‌ای بود حتماً بهم بگین
آره هر چیزی دیدم میگم
 وقتتون بخیر
سلام خوبی
ممنون
ببخشید مزاح شد
قربونتون برم در خدمت یه مسئله‌ای الان یادم افتاده بود یکی اینکه یه ویس گذاشتم اونو گوش بده
بعد یه موضوع دیگه‌ای هم که هست توی قسمت خرید و فروش
این این مدلی هم باشه که مثلاً ۱۰۰۰ دلار از امیر حسین بخریم هم یعنی اینطوری باشه بای سل بای و سل همزمان با همدیگه انجام بشه
یکم با ی‌کنین گازیه رو ببخشید
ببین شما بای می‌کنی از امیرحسین ثبت میشه تموم میشه میره تو سیستم
خب
سل می‌کنی به وهاب
خب
اوکی
این واسه اون زمانیه که مثلاً ۱۰۰ هزار دلار امیرحسین می‌خری اوکی
۲۰ هزار دلارشو به وهاب می‌فروشید جدا جدا باید اینا رو بفروشید دیگه درسته آره
بعد وق مثلاً بعضاًم میبینی که ه۰۰ دلار از امیرحسین می‌خری
همون هزار دلار و اینام میش ش به
اه
اون توی یک صفحه باشه راحت‌تره دیگه
آره
متوجه شدم ب
یعنی هر دو تا کاستومرو از اینور انتخاب می‌کنی
یه ریت واسه خریدت می‌زنی
یه ریت واسه فروشت می‌زنی
اون ریتی که واسه فروش زدی میره میفته توی اون کسی که براش فروختی اون ریتی که واسه خریدش زدی میفته تو صفحه اون شخصی که ازش خریدی
متوجه شدم بله نی بای سل بای و سل با همدیگه این چون یه یه لحظه به ذهنم اومد چون تو هم داری روی اون قسمت کار می‌کنی گفتم آپدیتت
ممنونم ازتون مرسی آره متوجه شدم
خواهش می‌کنم مرسی
مرسی از شما خدانگهدار
خداحافظ