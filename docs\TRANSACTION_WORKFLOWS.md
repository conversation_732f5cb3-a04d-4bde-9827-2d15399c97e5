# Arena Doviz - Transaction Workflows

## Overview
This document defines the detailed workflows for all transaction types in the Arena Doviz system. Each workflow includes process steps, validation rules, user interactions, and system responses.

## Workflow Diagrams

### 1. Currency Purchase Workflow (Customer → Exchange Office)

**Process Flow:**
1. Customer requests currency purchase
2. Employee selects buy transaction type
3. Enter transaction details (amount, currency, rate, commission)
4. System validates transaction data
5. Calculate commission and total amount
6. Preview transaction for confirmation
7. Create transaction record in database
8. Update customer and company balances
9. Generate unique transaction number
10. Send WhatsApp notification to customer group
11. Check if approval is required
12. If approved/no approval needed: mark as completed and generate receipt
13. If rejected: mark as cancelled and notify employee

### 2. Currency Sale Workflow (Exchange Office → Customer)

```mermaid
graph TD
    A[Customer Requests Currency Sale] --> B[Employee Selects Sell Transaction]
    B --> C[Enter Transaction Details]
    C --> D{Balance Check}
    D -->|Insufficient| E[Show Balance Error]
    D -->|Sufficient| F[Calculate Commission & Total]
    E --> C
    F --> G[Preview Transaction]
    G --> H{Employee Confirms?}
    H -->|No| C
    H -->|Yes| I[Create Transaction Record]
    I --> J[Update Customer Balance]
    J --> K[Update Company Balance]
    K --> L[Generate Transaction Number]
    L --> M[Send WhatsApp Notification]
    M --> N{Requires Approval?}
    N -->|No| O[Mark as Completed]
    N -->|Yes| P[Mark as Pending Approval]
    P --> Q[Notify Approver]
    Q --> R{Approved?}
    R -->|Yes| O
    R -->|No| S[Mark as Cancelled]
    O --> T[Generate Receipt]
    T --> U[Transaction Complete]
    S --> V[Notify Employee]
```

### 3. Internal Transfer Workflow (Customer A → Customer B)

```mermaid
graph TD
    A[Employee Initiates Transfer] --> B[Select Source Customer]
    B --> C[Select Destination Customer]
    C --> D[Enter Transfer Details]
    D --> E{Source Balance Check}
    E -->|Insufficient| F[Show Balance Error]
    E -->|Sufficient| G[Calculate Commission]
    F --> D
    G --> H[Preview Transfer]
    H --> I{Employee Confirms?}
    I -->|No| D
    I -->|Yes| J[Create Transfer Record]
    J --> K[Debit Source Customer]
    K --> L[Credit Destination Customer]
    L --> M[Update Company Balance]
    M --> N[Generate Transaction Numbers]
    N --> O[Send WhatsApp Notifications]
    O --> P{Requires Approval?}
    P -->|No| Q[Mark as Completed]
    P -->|Yes| R[Mark as Pending Approval]
    R --> S[Notify Approver]
    S --> T{Approved?}
    T -->|Yes| Q
    T -->|No| U[Reverse Transaction]
    Q --> V[Generate Receipts]
    V --> W[Transfer Complete]
    U --> X[Notify Employee]
```

### 4. SWIFT Transfer Workflow

```mermaid
graph TD
    A[Employee Initiates SWIFT] --> B{Transfer Type}
    B -->|Incoming| C[Enter Incoming Details]
    B -->|Outgoing| D[Enter Outgoing Details]
    C --> E[Upload SWIFT Documentation]
    D --> F[Upload SWIFT Documentation]
    E --> G[Enter Bank Details]
    F --> H[Enter Bank Details]
    G --> I[Calculate Fees]
    H --> I
    I --> J[Preview Transaction]
    J --> K{Employee Confirms?}
    K -->|No| L{Type Check}
    L -->|Incoming| C
    L -->|Outgoing| D
    K -->|Yes| M[Create SWIFT Record]
    M --> N[Update Customer Balance]
    N --> O[Update Company Balance]
    O --> P[Generate Transaction Number]
    P --> Q[Send WhatsApp Notification]
    Q --> R{Requires Approval?}
    R -->|No| S[Mark as Completed]
    R -->|Yes| T[Mark as Pending Approval]
    T --> U[Notify Approver]
    U --> V{Approved?}
    V -->|Yes| S
    V -->|No| W[Mark as Cancelled]
    S --> X[Generate Documentation]
    X --> Y[SWIFT Complete]
    W --> Z[Notify Employee]
```

### 5. Cash Deposit/Withdrawal Workflow

```mermaid
graph TD
    A[Employee Initiates Cash Transaction] --> B{Transaction Type}
    B -->|Deposit| C[Select Customer]
    B -->|Withdrawal| D[Select Customer]
    C --> E[Enter Deposit Amount]
    D --> F[Enter Withdrawal Amount]
    E --> G[Select Currency]
    F --> H{Balance Check}
    G --> I[Calculate Exchange Rate]
    H -->|Insufficient| J[Show Balance Error]
    H -->|Sufficient| K[Select Currency]
    J --> F
    K --> L[Calculate Exchange Rate]
    I --> M[Preview Transaction]
    L --> M
    M --> N{Employee Confirms?}
    N -->|No| O{Type Check}
    O -->|Deposit| E
    O -->|Withdrawal| F
    N -->|Yes| P[Create Cash Record]
    P --> Q[Update Customer Balance]
    Q --> R[Update Company Cash Balance]
    R --> S[Generate Transaction Number]
    S --> T[Send WhatsApp Notification]
    T --> U[Generate Receipt]
    U --> V[Cash Transaction Complete]
```

## Detailed Process Specifications

### Transaction Validation Rules

#### Common Validations
- **Amount**: Must be positive, within daily limits
- **Currency**: Must be active and supported
- **Location**: Must be active and have current rates
- **Customer**: Must be active and verified
- **Rate**: Must be current (within 24 hours) or manually entered
- **Commission**: Must be within configured limits

#### Specific Validations
- **Buy Transactions**: Company must have sufficient currency inventory
- **Sell Transactions**: Customer must have sufficient balance
- **Transfers**: Source customer must have sufficient balance
- **SWIFT**: All required documentation must be uploaded
- **Cash**: Physical cash availability must be confirmed

### Commission Calculation Logic

#### Percentage Commission
```
Commission Amount = Transaction Amount × (Commission Rate / 100)
Final Amount = Transaction Amount ± Commission Amount
```

#### Fixed Commission
```
Commission Amount = Fixed Commission Value
Final Amount = Transaction Amount ± Commission Amount
```

#### Currency Selection for Commission
- **Same Currency**: Commission deducted from transaction currency
- **Different Currency**: Commission converted at current rate

### Balance Update Logic

#### Double-Entry Accounting
Every transaction creates two entries:
1. **Customer Account**: Debit or Credit based on transaction type
2. **Company Account**: Opposite entry to maintain balance

#### Balance Calculation
```sql
-- Customer Balance Update
UPDATE customer_balances 
SET balance = balance + CASE 
    WHEN transaction_type = 'buy' THEN +amount
    WHEN transaction_type = 'sell' THEN -amount
    WHEN transaction_type = 'transfer_in' THEN +amount
    WHEN transaction_type = 'transfer_out' THEN -amount
END
WHERE customer_id = ? AND currency_id = ?;

-- Company Balance Update
UPDATE company_balances 
SET balance = balance + CASE 
    WHEN transaction_type = 'buy' THEN -amount
    WHEN transaction_type = 'sell' THEN +amount
END
WHERE location_id = ? AND currency_id = ?;
```

### Approval Workflow

#### Approval Requirements
- **Amount Threshold**: Transactions above configured limit
- **Customer Risk Level**: High-risk customers require approval
- **Transaction Type**: SWIFT transfers always require approval
- **User Role**: Viewers cannot approve transactions

#### Approval Process
1. **Notification**: Approver receives notification
2. **Review**: Approver reviews transaction details
3. **Decision**: Approve, reject, or request modification
4. **Action**: System processes approval decision
5. **Notification**: All parties notified of decision

### WhatsApp Notification System

#### Message Templates

##### Transaction Notification
```
🏦 Arena Doviz - Transaction Notification

Customer: {customer_name}
Transaction: #{transaction_number}
Type: {transaction_type}
Amount: {amount} {currency}
Rate: {exchange_rate}
Commission: {commission}
Date: {transaction_date}

Status: {status}
```

##### Approval Request
```
⚠️ Arena Doviz - Approval Required

Transaction #{transaction_number} requires approval
Customer: {customer_name}
Amount: {amount} {currency}
Type: {transaction_type}
Created by: {employee_name}

Please review and approve/reject.
```

#### Group Management
- **Automatic Creation**: New customer triggers group creation
- **Member Addition**: 7-8 company staff + customer
- **Message Routing**: All transaction notifications sent to group
- **Approval Workflow**: Approval requests sent to management group

### Error Handling

#### Common Error Scenarios
- **Insufficient Balance**: Clear message with current balance
- **Invalid Rate**: Prompt to update exchange rates
- **Network Issues**: Queue notifications for retry
- **Validation Errors**: Highlight specific fields with errors
- **System Errors**: Log error and show user-friendly message

#### Recovery Procedures
- **Transaction Rollback**: Automatic rollback on system errors
- **Balance Reconciliation**: Daily balance verification
- **Notification Retry**: Failed notifications queued for retry
- **Data Integrity**: Regular integrity checks and alerts

### Audit Trail

#### Logged Information
- **User Actions**: All user interactions logged
- **Data Changes**: Before/after values for all changes
- **System Events**: Automatic processes and their results
- **Error Events**: All errors with context and stack traces
- **Performance Metrics**: Response times and resource usage

#### Audit Log Format
```json
{
    "timestamp": "2025-01-15T10:30:00Z",
    "user_id": 123,
    "action": "transaction_create",
    "resource_type": "transaction",
    "resource_id": 456,
    "changes": {
        "before": null,
        "after": {
            "amount": 1000,
            "currency": "USD",
            "customer_id": 789
        }
    },
    "ip_address": "*************",
    "user_agent": "Mozilla/5.0...",
    "session_id": "abc123"
}
```

### Performance Considerations

#### Transaction Processing
- **Batch Processing**: Multiple transactions processed together
- **Queue System**: Heavy operations moved to background queues
- **Database Optimization**: Proper indexing for transaction queries
- **Caching**: Frequently accessed data cached in Redis

#### Scalability
- **Horizontal Scaling**: Support for multiple application servers
- **Database Sharding**: Large transaction tables can be partitioned
- **Load Balancing**: Distribute load across multiple servers
- **CDN Integration**: Static assets served from CDN

### Integration Points

#### External Systems
- **WhatsApp API**: For automated notifications
- **Currency APIs**: For real-time exchange rates
- **Banking APIs**: For SWIFT and bank transfer integration
- **SMS Services**: For backup notifications
- **Email Services**: For formal communications

#### Internal Systems
- **Reporting Engine**: Transaction data feeds reports
- **Analytics System**: Transaction patterns and insights
- **Backup System**: Regular transaction data backups
- **Monitoring System**: Transaction processing metrics
